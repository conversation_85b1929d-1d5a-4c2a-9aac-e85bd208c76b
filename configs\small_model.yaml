# Small model configuration for quick experimentation
# Device options: "auto" (automatic detection), "cpu", "cuda", "mps" (for Apple Silicon)
# For CPU training, consider using cpu_small_model.yaml for optimized settings
device: "auto"

model:
  vocab_size: 32000
  d_model: 256
  n_heads: 4
  n_layers: 4
  d_ff: 1024
  max_seq_len: 512
  dropout: 0.1
  attention_dropout: 0.1
  activation: "gelu"
  pre_norm: true

training:
  batch_size: 16
  learning_rate: 0.0005
  weight_decay: 0.01
  num_epochs: 5
  lr_scheduler: "cosine"
  warmup_steps: 500
  optimizer: "adamw"
  gradient_accumulation_steps: 2
  use_amp: true
  save_steps: 1000
  eval_steps: 500
  logging_steps: 50
  # DataLoader settings (adjust for CPU if needed)
  dataloader_num_workers: 4
  dataloader_pin_memory: true

data:
  dataset_name: "wikitext"
  dataset_config: "wikitext-2-raw-v1"
  max_length: 512
  vocab_size: 32000
  pack_sequences: true
