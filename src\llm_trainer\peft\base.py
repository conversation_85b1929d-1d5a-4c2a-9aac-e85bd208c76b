"""
Base classes and utilities for Parameter-Efficient Fine-Tuning (PEFT).

This module provides the foundational classes and interfaces for all PEFT methods,
designed to be more flexible and efficient than existing implementations.
"""

import torch
import torch.nn as nn
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Union, Any, Tuple, Set
from enum import Enum
import re
import warnings


class TaskType(str, Enum):
    """Supported task types for PEFT."""
    CAUSAL_LM = "CAUSAL_LM"
    SEQ_2_SEQ_LM = "SEQ_2_SEQ_LM"
    TOKEN_CLS = "TOKEN_CLS"
    SEQ_CLS = "SEQ_CLS"
    QUESTION_ANS = "QUESTION_ANS"
    FEATURE_EXTRACTION = "FEATURE_EXTRACTION"


@dataclass
class PEFTConfig(ABC):
    """
    Base configuration class for all PEFT methods.
    
    This class defines the common interface and parameters shared across
    different PEFT approaches.
    """
    
    # Core PEFT parameters
    peft_type: str = field(default="", init=False)
    task_type: TaskType = TaskType.CAUSAL_LM
    inference_mode: bool = False
    
    # Target modules configuration
    target_modules: Optional[Union[List[str], str]] = None
    modules_to_save: Optional[List[str]] = None
    
    # Training parameters
    bias: str = "none"  # "none", "all", "lora_only"
    fan_in_fan_out: bool = False
    init_lora_weights: bool = True
    
    # Revision and model info
    revision: Optional[str] = None
    
    def __post_init__(self):
        """Post-initialization validation and setup."""
        self.peft_type = self.__class__.__name__.replace("Config", "").upper()
        
        # Validate bias parameter
        if self.bias not in ["none", "all", "lora_only"]:
            raise ValueError(f"bias must be one of ['none', 'all', 'lora_only'], got {self.bias}")
        
        # Convert target_modules to list if string
        if isinstance(self.target_modules, str):
            self.target_modules = [self.target_modules]
    
    @abstractmethod
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary."""
        pass
    
    @classmethod
    @abstractmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "PEFTConfig":
        """Create config from dictionary."""
        pass


class PEFTLayer(nn.Module, ABC):
    """
    Abstract base class for PEFT layers.
    
    All PEFT layer implementations should inherit from this class and implement
    the required abstract methods.
    """
    
    def __init__(self, base_layer: nn.Module, **kwargs):
        super().__init__()
        self.base_layer = base_layer
        self.merged = False
        self.disable_adapters = False
        
    @abstractmethod
    def merge(self) -> None:
        """Merge adapter weights into base layer."""
        pass
    
    @abstractmethod
    def unmerge(self) -> None:
        """Unmerge adapter weights from base layer."""
        pass
    
    @abstractmethod
    def forward(self, x: torch.Tensor, *args, **kwargs) -> torch.Tensor:
        """Forward pass through the PEFT layer."""
        pass
    
    def enable_adapters(self) -> None:
        """Enable adapter computation."""
        self.disable_adapters = False
    
    def disable_adapters_temporarily(self) -> None:
        """Temporarily disable adapter computation."""
        self.disable_adapters = True


class PEFTModel(nn.Module):
    """
    Base class for PEFT models.
    
    This class wraps a base model and adds PEFT capabilities while maintaining
    compatibility with the original model interface.
    """
    
    def __init__(self, model: nn.Module, config: PEFTConfig, adapter_name: str = "default"):
        super().__init__()
        self.base_model = model
        self.config = config
        self.adapter_name = adapter_name
        self.peft_config = {adapter_name: config}
        self.active_adapter = adapter_name
        self.peft_modules = {}
        self.merged = False
        
        # Store original forward method
        self._original_forward = model.forward
        
        # Apply PEFT to the model
        self._setup_peft()
    
    @abstractmethod
    def _setup_peft(self) -> None:
        """Setup PEFT layers in the model."""
        pass
    
    def add_adapter(self, adapter_name: str, config: PEFTConfig) -> None:
        """Add a new adapter to the model."""
        if adapter_name in self.peft_config:
            raise ValueError(f"Adapter {adapter_name} already exists")
        
        self.peft_config[adapter_name] = config
        # Implementation depends on specific PEFT method
        
    def set_adapter(self, adapter_name: str) -> None:
        """Set the active adapter."""
        if adapter_name not in self.peft_config:
            raise ValueError(f"Adapter {adapter_name} not found")
        self.active_adapter = adapter_name
    
    def merge_and_unload(self) -> nn.Module:
        """Merge adapter weights and return the base model."""
        if not self.merged:
            self.merge_adapter()
        
        # Return a copy of the base model with merged weights
        return self.base_model
    
    def merge_adapter(self) -> None:
        """Merge adapter weights into base model."""
        if self.merged:
            warnings.warn("Adapter already merged")
            return
        
        for module in self.modules():
            if isinstance(module, PEFTLayer):
                module.merge()
        
        self.merged = True
    
    def unmerge_adapter(self) -> None:
        """Unmerge adapter weights from base model."""
        if not self.merged:
            warnings.warn("Adapter not merged")
            return
        
        for module in self.modules():
            if isinstance(module, PEFTLayer):
                module.unmerge()
        
        self.merged = False
    
    def enable_adapter_layers(self) -> None:
        """Enable all adapter layers."""
        for module in self.modules():
            if isinstance(module, PEFTLayer):
                module.enable_adapters()
    
    def disable_adapter_layers(self) -> None:
        """Disable all adapter layers."""
        for module in self.modules():
            if isinstance(module, PEFTLayer):
                module.disable_adapters_temporarily()
    
    def get_peft_state_dict(self, adapter_name: Optional[str] = None) -> Dict[str, torch.Tensor]:
        """Get state dict containing only PEFT parameters."""
        if adapter_name is None:
            adapter_name = self.active_adapter
        
        state_dict = {}
        for name, param in self.named_parameters():
            if "lora_" in name or "adapter_" in name:
                state_dict[name] = param
        
        return state_dict
    
    def load_peft_state_dict(self, state_dict: Dict[str, torch.Tensor], 
                           adapter_name: Optional[str] = None) -> None:
        """Load PEFT parameters from state dict."""
        if adapter_name is None:
            adapter_name = self.active_adapter
        
        # Filter state dict to only include PEFT parameters
        peft_state_dict = {k: v for k, v in state_dict.items() 
                          if "lora_" in k or "adapter_" in k}
        
        self.load_state_dict(peft_state_dict, strict=False)
    
    def print_trainable_parameters(self) -> None:
        """Print the number of trainable parameters."""
        trainable_params = 0
        all_param = 0
        
        for _, param in self.named_parameters():
            all_param += param.numel()
            if param.requires_grad:
                trainable_params += param.numel()
        
        print(f"Trainable params: {trainable_params:,} || "
              f"All params: {all_param:,} || "
              f"Trainable%: {100 * trainable_params / all_param:.2f}%")
    
    def forward(self, *args, **kwargs):
        """Forward pass through the PEFT model."""
        return self.base_model(*args, **kwargs)


def get_peft_model(model: nn.Module, peft_config: PEFTConfig, 
                   adapter_name: str = "default") -> PEFTModel:
    """
    Create a PEFT model from a base model and configuration.
    
    Args:
        model: Base model to add PEFT to
        peft_config: PEFT configuration
        adapter_name: Name for the adapter
        
    Returns:
        PEFT model with adapters applied
    """
    # Import here to avoid circular imports
    from .lora import LoRAModel, LoRAConfig
    from .qlora import QLoRAModel, QLoRAConfig
    from .adalora import AdaLoRAModel, AdaLoRAConfig
    
    if isinstance(peft_config, LoRAConfig):
        return LoRAModel(model, peft_config, adapter_name)
    elif isinstance(peft_config, QLoRAConfig):
        return QLoRAModel(model, peft_config, adapter_name)
    elif isinstance(peft_config, AdaLoRAConfig):
        return AdaLoRAModel(model, peft_config, adapter_name)
    else:
        raise ValueError(f"Unsupported PEFT config type: {type(peft_config)}")


def _get_submodules(model: nn.Module, key: str) -> Tuple[nn.Module, nn.Module, str]:
    """
    Get parent module, target module, and target name from a key.
    
    Args:
        model: The model to search in
        key: Dot-separated key to the target module
        
    Returns:
        Tuple of (parent_module, target_module, target_name)
    """
    parent = model
    target_name = key.split(".")[-1]
    
    for attr in key.split(".")[:-1]:
        parent = getattr(parent, attr)
    
    target = getattr(parent, target_name)
    return parent, target, target_name


def _find_modules(model: nn.Module, target_modules: List[str]) -> Dict[str, nn.Module]:
    """
    Find all modules matching the target module patterns.
    
    Args:
        model: Model to search in
        target_modules: List of module name patterns to match
        
    Returns:
        Dictionary mapping module names to modules
    """
    found_modules = {}
    
    for name, module in model.named_modules():
        for target in target_modules:
            # Support regex patterns
            if re.search(target, name) or target in name:
                found_modules[name] = module
                break
    
    return found_modules
