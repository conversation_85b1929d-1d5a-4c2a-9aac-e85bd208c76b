"""
Utility functions for Parameter-Efficient Fine-Tuning (PEFT).

This module provides helper functions for working with PEFT models,
including parameter management, state dict operations, and model utilities.
"""

import torch
import torch.nn as nn
from typing import Dict, List, Optional, Union, Any, Tuple
import os
import json
import warnings
from pathlib import Path

from .base import PEFTModel, PEFTConfig, PEFTLayer


def mark_only_lora_as_trainable(model: nn.Module, bias: str = "none") -> None:
    """
    Mark only LoRA parameters as trainable.
    
    Args:
        model: The model to modify
        bias: Bias training strategy ("none", "all", "lora_only")
    """
    # First, freeze all parameters
    for param in model.parameters():
        param.requires_grad = False
    
    # Then, unfreeze LoRA parameters
    for name, param in model.named_parameters():
        if "lora_" in name or "adapter_" in name:
            param.requires_grad = True
    
    # Handle bias parameters
    if bias == "all":
        for name, param in model.named_parameters():
            if "bias" in name:
                param.requires_grad = True
    elif bias == "lora_only":
        for name, param in model.named_parameters():
            if "bias" in name and ("lora_" in name or "adapter_" in name):
                param.requires_grad = True


def get_peft_model_state_dict(
    model: Union[PEFTModel, nn.Module], 
    state_dict: Optional[Dict[str, torch.Tensor]] = None,
    adapter_name: str = "default"
) -> Dict[str, torch.Tensor]:
    """
    Get state dict containing only PEFT parameters.
    
    Args:
        model: PEFT model or regular model
        state_dict: Optional state dict to filter from
        adapter_name: Name of the adapter to get state dict for
        
    Returns:
        Dictionary containing only PEFT parameters
    """
    if state_dict is None:
        state_dict = model.state_dict()
    
    peft_state_dict = {}
    
    # Filter for PEFT parameters
    for name, param in state_dict.items():
        if any(peft_key in name for peft_key in ["lora_", "adapter_", "peft_"]):
            # Include adapter-specific parameters
            if adapter_name == "default" or adapter_name in name or "default" in name:
                peft_state_dict[name] = param
    
    return peft_state_dict


def set_peft_model_state_dict(
    model: Union[PEFTModel, nn.Module],
    peft_state_dict: Dict[str, torch.Tensor],
    adapter_name: str = "default"
) -> None:
    """
    Load PEFT parameters from state dict.
    
    Args:
        model: PEFT model to load parameters into
        peft_state_dict: State dict containing PEFT parameters
        adapter_name: Name of the adapter
    """
    # Filter state dict to only include PEFT parameters
    filtered_state_dict = {}
    for name, param in peft_state_dict.items():
        if any(peft_key in name for peft_key in ["lora_", "adapter_", "peft_"]):
            filtered_state_dict[name] = param
    
    # Load the filtered state dict
    missing_keys, unexpected_keys = model.load_state_dict(filtered_state_dict, strict=False)
    
    if missing_keys:
        warnings.warn(f"Missing keys when loading PEFT state dict: {missing_keys}")
    if unexpected_keys:
        warnings.warn(f"Unexpected keys when loading PEFT state dict: {unexpected_keys}")


def merge_and_unload(model: PEFTModel) -> nn.Module:
    """
    Merge PEFT weights and return the base model.
    
    Args:
        model: PEFT model to merge
        
    Returns:
        Base model with merged weights
    """
    if hasattr(model, 'merge_and_unload'):
        return model.merge_and_unload()
    else:
        # Fallback for models without merge_and_unload method
        if hasattr(model, 'merge_adapter'):
            model.merge_adapter()
        return model.base_model if hasattr(model, 'base_model') else model


def save_peft_model(
    model: Union[PEFTModel, nn.Module],
    save_directory: Union[str, Path],
    adapter_name: str = "default",
    save_config: bool = True,
    safe_serialization: bool = False
) -> None:
    """
    Save PEFT model weights and configuration.
    
    Args:
        model: PEFT model to save
        save_directory: Directory to save the model
        adapter_name: Name of the adapter to save
        save_config: Whether to save the configuration
        safe_serialization: Whether to use safe serialization (safetensors)
    """
    save_directory = Path(save_directory)
    save_directory.mkdir(parents=True, exist_ok=True)
    
    # Get PEFT state dict
    peft_state_dict = get_peft_model_state_dict(model, adapter_name=adapter_name)
    
    # Save weights
    if safe_serialization:
        try:
            from safetensors.torch import save_file
            save_file(peft_state_dict, save_directory / "adapter_model.safetensors")
        except ImportError:
            warnings.warn("safetensors not available, falling back to torch.save")
            torch.save(peft_state_dict, save_directory / "adapter_model.bin")
    else:
        torch.save(peft_state_dict, save_directory / "adapter_model.bin")
    
    # Save configuration
    if save_config and hasattr(model, 'peft_config'):
        config = model.peft_config.get(adapter_name)
        if config:
            config_dict = config.to_dict()
            with open(save_directory / "adapter_config.json", "w") as f:
                json.dump(config_dict, f, indent=2)


def load_peft_model(
    model: nn.Module,
    model_id: Union[str, Path],
    adapter_name: str = "default",
    config: Optional[PEFTConfig] = None,
    **kwargs
) -> PEFTModel:
    """
    Load PEFT model from saved weights and configuration.
    
    Args:
        model: Base model to add PEFT to
        model_id: Path to saved PEFT model
        adapter_name: Name for the adapter
        config: Optional PEFT configuration (if not loading from file)
        
    Returns:
        PEFT model with loaded weights
    """
    model_path = Path(model_id)
    
    # Load configuration
    if config is None:
        config_path = model_path / "adapter_config.json"
        if config_path.exists():
            with open(config_path, "r") as f:
                config_dict = json.load(f)
            
            # Import config classes
            from .lora import LoRAConfig
            from .qlora import QLoRAConfig
            
            peft_type = config_dict.get("peft_type", "LORA")
            if peft_type == "LORA":
                config = LoRAConfig.from_dict(config_dict)
            elif peft_type == "QLORA":
                config = QLoRAConfig.from_dict(config_dict)
            else:
                raise ValueError(f"Unsupported PEFT type: {peft_type}")
        else:
            raise ValueError("No configuration found and none provided")
    
    # Create PEFT model
    from .base import get_peft_model
    peft_model = get_peft_model(model, config, adapter_name)
    
    # Load weights
    weight_files = [
        model_path / "adapter_model.safetensors",
        model_path / "adapter_model.bin"
    ]
    
    weight_file = None
    for file_path in weight_files:
        if file_path.exists():
            weight_file = file_path
            break
    
    if weight_file is None:
        raise FileNotFoundError(f"No weight file found in {model_path}")
    
    # Load state dict
    if weight_file.suffix == ".safetensors":
        try:
            from safetensors.torch import load_file
            state_dict = load_file(weight_file)
        except ImportError:
            raise ImportError("safetensors required to load .safetensors files")
    else:
        state_dict = torch.load(weight_file, map_location="cpu")
    
    # Load into model
    set_peft_model_state_dict(peft_model, state_dict, adapter_name)
    
    return peft_model


def print_trainable_parameters(model: nn.Module) -> Dict[str, int]:
    """
    Print and return the number of trainable parameters.
    
    Args:
        model: Model to analyze
        
    Returns:
        Dictionary with parameter counts
    """
    trainable_params = 0
    all_param = 0
    
    for name, param in model.named_parameters():
        all_param += param.numel()
        if param.requires_grad:
            trainable_params += param.numel()
    
    percentage = 100 * trainable_params / all_param if all_param > 0 else 0
    
    print(f"Trainable params: {trainable_params:,} || "
          f"All params: {all_param:,} || "
          f"Trainable%: {percentage:.2f}%")
    
    return {
        "trainable_params": trainable_params,
        "all_param": all_param,
        "trainable_percentage": percentage
    }


def get_peft_model_memory_usage(model: Union[PEFTModel, nn.Module]) -> Dict[str, float]:
    """
    Get memory usage information for a PEFT model.
    
    Args:
        model: PEFT model to analyze
        
    Returns:
        Dictionary with memory usage information
    """
    total_params = 0
    trainable_params = 0
    frozen_params = 0
    
    for name, param in model.named_parameters():
        param_count = param.numel()
        total_params += param_count
        
        if param.requires_grad:
            trainable_params += param_count
        else:
            frozen_params += param_count
    
    # Estimate memory usage (bytes)
    # Assuming FP32 for simplicity (4 bytes per parameter)
    total_memory = total_params * 4
    trainable_memory = trainable_params * 4
    frozen_memory = frozen_params * 4
    
    return {
        "total_params": total_params,
        "trainable_params": trainable_params,
        "frozen_params": frozen_params,
        "total_memory_mb": total_memory / (1024 * 1024),
        "trainable_memory_mb": trainable_memory / (1024 * 1024),
        "frozen_memory_mb": frozen_memory / (1024 * 1024),
        "memory_efficiency": trainable_memory / total_memory if total_memory > 0 else 0
    }


def find_all_linear_names(model: nn.Module, exclude_names: Optional[List[str]] = None) -> List[str]:
    """
    Find all linear layer names in a model.
    
    Args:
        model: Model to search
        exclude_names: List of names to exclude
        
    Returns:
        List of linear layer names
    """
    if exclude_names is None:
        exclude_names = []
    
    linear_names = []
    for name, module in model.named_modules():
        if isinstance(module, nn.Linear):
            # Check if name should be excluded
            should_exclude = any(exclude in name for exclude in exclude_names)
            if not should_exclude:
                linear_names.append(name)
    
    return linear_names


def prepare_model_for_peft(
    model: nn.Module,
    peft_config: PEFTConfig,
    enable_gradient_checkpointing: bool = True
) -> nn.Module:
    """
    Prepare a model for PEFT training.
    
    Args:
        model: Base model to prepare
        peft_config: PEFT configuration
        enable_gradient_checkpointing: Whether to enable gradient checkpointing
        
    Returns:
        Prepared model
    """
    # Enable gradient checkpointing for memory efficiency
    if enable_gradient_checkpointing and hasattr(model, 'gradient_checkpointing_enable'):
        model.gradient_checkpointing_enable()
    
    # Cast certain modules to float32 for stability
    for name, module in model.named_modules():
        if isinstance(module, (nn.LayerNorm, nn.GroupNorm, nn.BatchNorm1d, nn.BatchNorm2d)):
            module.float()
    
    return model
