# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Machine Learning / Data Science
*.pkl
*.pickle
*.joblib
*.h5
*.hdf5
*.tfrecord
*.tfrecords

# Model files and checkpoints
checkpoints/
# models/
saved_models/
weights/
*.pth
*.pt
*.bin
*.safetensors
*.ckpt
*.model

# Training outputs
logs/
runs/
outputs/
output/
results/
experiments/

# TensorBoard logs
events.out.tfevents.*
tb_logs/
tensorboard_logs/

# Weights & Biases
wandb/

# MLflow
mlruns/

# Data files
# data/
datasets/
*.csv
*.tsv
*.json
*.jsonl
*.parquet
*.feather
*.arrow

# Cache directories
.cache/
cache/
__pycache__/
.transformers_cache/
.datasets_cache/
.huggingface/

# Configuration files with secrets
config/secrets.yaml
config/secrets.json
.secrets
secrets.env

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
tmp/
temp/

# Large files that shouldn't be committed
*.zip
*.tar.gz
*.rar
*.7z

# Virtual environment
conda-meta/
.conda/

# CUDA cache
.nv/

# PyTorch specific
torch_extensions/

# Hugging Face cache
.cache/huggingface/
.qodo
# Local development
local/
scratch/
playground/
test_outputs/

# API keys and credentials
*.key
*.pem
credentials.json
token.txt
api_keys.txt
v2.md
# Project specific
# Uncomment and modify these based on your specific needs
# my_custom_data/
# private_configs/
# local_experiments/
