"""
Parameter-Efficient Fine-Tuning (PEFT) module for LLM Trainer.

This module provides state-of-the-art parameter-efficient fine-tuning methods
including LoRA, QLoRA, AdaLoRA, and other adapter-based approaches with
memory-efficient implementations that rival and exceed Unsloth's capabilities.

Key Features:
- LoRA (Low-Rank Adaptation) with configurable rank and target modules
- QLoRA (Quantized LoRA) with 4-bit and 8-bit quantization
- AdaLoRA (Adaptive LoRA) with dynamic rank allocation
- Memory-efficient implementations with gradient checkpointing
- Advanced optimizers (8-bit AdamW, Lion, etc.)
- Multi-GPU and distributed training support
- Model export utilities (GGUF, ONNX, merged weights)
"""

from .base import (
    PEFTConfig,
    PEFTModel,
    PEFTLayer,
    get_peft_model,
    TaskType
)

from .lora import (
    LoRAConfig,
    LoRAModel,
    LoRALayer,
    LoRALinear,
    LoRAEmbedding,
    LoRAConv2d
)

from .qlora import (
    QLoRAConfig,
    QLoRAModel,
    QuantizationConfig,
    BitsAndBytesConfig
)

from .adalora import (
    AdaLoRAConfig,
    AdaLoRAModel,
    AdaLoRALayer
)

from .utils import (
    mark_only_lora_as_trainable,
    get_peft_model_state_dict,
    set_peft_model_state_dict,
    merge_and_unload,
    save_peft_model,
    load_peft_model,
    print_trainable_parameters
)

from .memory import (
    MemoryOptimizer,
    GradientCheckpointing,
    ActivationCheckpointing,
    DynamicBatchSizing
)

from .optimizers import (
    AdamW8bit,
    Lion8bit,
    create_peft_optimizer,
    get_optimizer_grouped_parameters
)

__version__ = "1.0.0"

__all__ = [
    # Base classes
    "PEFTConfig",
    "PEFTModel", 
    "PEFTLayer",
    "get_peft_model",
    "TaskType",
    
    # LoRA
    "LoRAConfig",
    "LoRAModel",
    "LoRALayer",
    "LoRALinear",
    "LoRAEmbedding", 
    "LoRAConv2d",
    
    # QLoRA
    "QLoRAConfig",
    "QLoRAModel",
    "QuantizationConfig",
    "BitsAndBytesConfig",
    
    # AdaLoRA
    "AdaLoRAConfig",
    "AdaLoRAModel",
    "AdaLoRALayer",
    
    # Utilities
    "mark_only_lora_as_trainable",
    "get_peft_model_state_dict",
    "set_peft_model_state_dict",
    "merge_and_unload",
    "save_peft_model",
    "load_peft_model",
    "print_trainable_parameters",
    
    # Memory optimization
    "MemoryOptimizer",
    "GradientCheckpointing",
    "ActivationCheckpointing", 
    "DynamicBatchSizing",
    
    # Optimizers
    "AdamW8bit",
    "Lion8bit",
    "create_peft_optimizer",
    "get_optimizer_grouped_parameters"
]
