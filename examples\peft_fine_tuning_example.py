"""
Example script demonstrating Parameter-Efficient Fine-Tuning (PEFT) with the LLM Trainer.

This script shows how to use LoRA and QLoRA for memory-efficient fine-tuning
of large language models, similar to Unsloth but with enhanced capabilities.
"""

import torch
import torch.nn as nn
from pathlib import Path
import sys

# Add the src directory to the path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from llm_trainer import (
    TransformerLM, ModelConfig, TrainingConfig, Trainer,
    LoRAConfig, QLoRAConfig, get_peft_model, 
    print_trainable_parameters, save_peft_model, load_peft_model
)


def create_sample_model():
    """Create a sample transformer model for demonstration."""
    config = ModelConfig(
        vocab_size=32000,
        d_model=512,
        n_heads=8,
        n_layers=6,
        d_ff=2048,
        max_seq_len=2048,
        dropout=0.1
    )
    
    model = TransformerLM(config)
    return model, config


def demonstrate_lora_fine_tuning():
    """Demonstrate LoRA fine-tuning."""
    print("=" * 60)
    print("LoRA Fine-Tuning Demonstration")
    print("=" * 60)
    
    # Create base model
    model, model_config = create_sample_model()
    print(f"Created base model with {sum(p.numel() for p in model.parameters()):,} parameters")
    
    # Configure LoRA
    lora_config = LoRAConfig(
        r=16,                    # Rank of adaptation
        lora_alpha=32,          # LoRA scaling parameter
        target_modules=[         # Target modules for LoRA
            "attention.q_proj",
            "attention.k_proj", 
            "attention.v_proj",
            "attention.o_proj",
            "feed_forward.w1",
            "feed_forward.w2",
            "feed_forward.w3"
        ],
        lora_dropout=0.1,       # LoRA dropout
        bias="none",            # Bias training strategy
        task_type="CAUSAL_LM"   # Task type
    )
    
    # Create PEFT model
    peft_model = get_peft_model(model, lora_config)
    
    # Print parameter information
    print("\nParameter breakdown after applying LoRA:")
    print_trainable_parameters(peft_model)
    
    # Demonstrate training setup
    training_config = TrainingConfig(
        batch_size=4,
        learning_rate=2e-4,     # Higher learning rate for LoRA
        num_epochs=3,
        gradient_accumulation_steps=4,
        use_amp=True,           # Mixed precision training
        max_grad_norm=1.0
    )
    
    print(f"\nTraining configuration:")
    print(f"  Batch size: {training_config.batch_size}")
    print(f"  Learning rate: {training_config.learning_rate}")
    print(f"  Mixed precision: {training_config.use_amp}")
    
    # Save the PEFT model
    save_dir = Path("./lora_model_example")
    save_peft_model(peft_model, save_dir)
    print(f"\nSaved LoRA model to {save_dir}")
    
    return peft_model, lora_config


def demonstrate_qlora_fine_tuning():
    """Demonstrate QLoRA fine-tuning with quantization."""
    print("\n" + "=" * 60)
    print("QLoRA Fine-Tuning Demonstration")
    print("=" * 60)
    
    # Create base model
    model, model_config = create_sample_model()
    
    # Configure QLoRA with 4-bit quantization
    qlora_config = QLoRAConfig(
        r=64,                   # Higher rank for QLoRA
        lora_alpha=16,
        target_modules=[
            "attention.q_proj",
            "attention.k_proj",
            "attention.v_proj", 
            "attention.o_proj"
        ],
        lora_dropout=0.05,
        bias="none",
        task_type="CAUSAL_LM",
        # Quantization settings
        quantization_config={
            "load_in_4bit": True,
            "bnb_4bit_compute_dtype": torch.float16,
            "bnb_4bit_quant_type": "nf4",
            "bnb_4bit_use_double_quant": True
        }
    )
    
    try:
        # Create QLoRA model (requires bitsandbytes)
        qlora_model = get_peft_model(model, qlora_config)
        
        print("\nParameter breakdown after applying QLoRA:")
        print_trainable_parameters(qlora_model)
        
        # Get memory usage information
        if hasattr(qlora_model, 'get_memory_footprint'):
            memory_info = qlora_model.get_memory_footprint()
            print(f"\nMemory usage:")
            print(f"  Total memory: {memory_info['total_memory_mb']:.1f} MB")
            print(f"  Memory reduction: {memory_info['memory_reduction']*100:.1f}%")
        
        # Save the QLoRA model
        save_dir = Path("./qlora_model_example")
        save_peft_model(qlora_model, save_dir)
        print(f"\nSaved QLoRA model to {save_dir}")
        
        return qlora_model, qlora_config
        
    except ImportError:
        print("\nQLoRA requires bitsandbytes library.")
        print("Install with: pip install bitsandbytes")
        return None, qlora_config


def demonstrate_multi_adapter():
    """Demonstrate multiple adapters on the same model."""
    print("\n" + "=" * 60)
    print("Multi-Adapter Demonstration")
    print("=" * 60)
    
    # Create base model
    model, model_config = create_sample_model()
    
    # Create first adapter for general fine-tuning
    general_config = LoRAConfig(
        r=8,
        lora_alpha=16,
        target_modules=["attention.q_proj", "attention.v_proj"],
        lora_dropout=0.1,
        task_type="CAUSAL_LM"
    )
    
    # Create PEFT model with first adapter
    peft_model = get_peft_model(model, general_config, adapter_name="general")
    
    # Add second adapter for specific task
    task_config = LoRAConfig(
        r=16,
        lora_alpha=32,
        target_modules=["attention.q_proj", "attention.k_proj", "attention.v_proj"],
        lora_dropout=0.05,
        task_type="CAUSAL_LM"
    )
    
    peft_model.add_adapter("task_specific", task_config)
    
    print("Created model with multiple adapters:")
    print(f"  Adapters: {list(peft_model.peft_config.keys())}")
    print(f"  Active adapter: {peft_model.active_adapter}")
    
    # Switch between adapters
    print("\nSwitching to task-specific adapter...")
    peft_model.set_adapter("task_specific")
    print(f"  Active adapter: {peft_model.active_adapter}")
    
    print_trainable_parameters(peft_model)
    
    return peft_model


def demonstrate_inference_optimization():
    """Demonstrate inference optimizations."""
    print("\n" + "=" * 60)
    print("Inference Optimization Demonstration")
    print("=" * 60)
    
    # Create and train a LoRA model
    model, _ = create_sample_model()
    lora_config = LoRAConfig(r=8, lora_alpha=16, target_modules=["attention.q_proj"])
    peft_model = get_peft_model(model, lora_config)
    
    # Create sample input
    batch_size, seq_len = 2, 128
    input_ids = torch.randint(0, 1000, (batch_size, seq_len))
    
    print("Testing inference modes:")
    
    # 1. Standard PEFT inference
    print("\n1. Standard PEFT inference (adapters active)")
    with torch.no_grad():
        output1 = peft_model(input_ids)
    print(f"   Output shape: {output1.shape}")
    
    # 2. Disabled adapters (base model only)
    print("\n2. Disabled adapters (base model inference)")
    peft_model.disable_adapter_layers()
    with torch.no_grad():
        output2 = peft_model(input_ids)
    print(f"   Output shape: {output2.shape}")
    
    # 3. Merged inference (fastest)
    print("\n3. Merged inference (adapters merged into base model)")
    peft_model.enable_adapter_layers()
    peft_model.merge_adapter()
    with torch.no_grad():
        output3 = peft_model(input_ids)
    print(f"   Output shape: {output3.shape}")
    
    # 4. Export merged model
    print("\n4. Exporting merged model")
    merged_model = peft_model.merge_and_unload()
    print(f"   Exported model type: {type(merged_model)}")
    
    return merged_model


def main():
    """Main demonstration function."""
    print("LLM Trainer PEFT Demonstration")
    print("Enhanced Parameter-Efficient Fine-Tuning")
    print("Rivaling and exceeding Unsloth capabilities")
    
    # Set device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"\nUsing device: {device}")
    
    try:
        # Demonstrate LoRA
        lora_model, lora_config = demonstrate_lora_fine_tuning()
        
        # Demonstrate QLoRA
        qlora_model, qlora_config = demonstrate_qlora_fine_tuning()
        
        # Demonstrate multi-adapter
        multi_adapter_model = demonstrate_multi_adapter()
        
        # Demonstrate inference optimization
        merged_model = demonstrate_inference_optimization()
        
        print("\n" + "=" * 60)
        print("Demonstration completed successfully!")
        print("=" * 60)
        
        print("\nKey features demonstrated:")
        print("✓ LoRA fine-tuning with configurable parameters")
        print("✓ QLoRA with 4-bit quantization (if bitsandbytes available)")
        print("✓ Multiple adapters on the same model")
        print("✓ Adapter switching and management")
        print("✓ Memory-efficient training")
        print("✓ Inference optimizations")
        print("✓ Model merging and export")
        
        print("\nNext steps:")
        print("- Integrate with your training pipeline")
        print("- Experiment with different LoRA ranks and targets")
        print("- Try QLoRA for large models (>7B parameters)")
        print("- Use multiple adapters for different tasks")
        
    except Exception as e:
        print(f"\nError during demonstration: {e}")
        print("This might be due to missing dependencies or hardware limitations.")


if __name__ == "__main__":
    main()
