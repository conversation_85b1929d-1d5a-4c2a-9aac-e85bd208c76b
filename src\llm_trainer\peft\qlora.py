"""
QLoRA (Quantized LoRA) implementation for memory-efficient fine-tuning.

This module provides QLoRA implementation with 4-bit and 8-bit quantization
support, designed to be more memory-efficient than existing implementations
while maintaining training quality.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Union, Any, Tuple
import warnings

from .base import PEFTConfig, TaskType
from .lora import LoRAModel, LoRAConfig, LoRALayer, LoRALinear

try:
    import bitsandbytes as bnb
    from bitsandbytes.nn import Linear4bit, Linear8bitLt
    HAS_BITSANDBYTES = True
except ImportError:
    HAS_BITSANDBYTES = False
    Linear4bit = None
    Linear8bitLt = None


@dataclass
class QuantizationConfig:
    """Configuration for quantization parameters."""
    
    load_in_4bit: bool = False
    load_in_8bit: bool = False
    bnb_4bit_compute_dtype: torch.dtype = torch.float16
    bnb_4bit_quant_type: str = "nf4"  # "nf4" or "fp4"
    bnb_4bit_use_double_quant: bool = True
    bnb_4bit_quant_storage: torch.dtype = torch.uint8
    llm_int8_threshold: float = 6.0
    llm_int8_skip_modules: Optional[List[str]] = None
    llm_int8_enable_fp32_cpu_offload: bool = False
    llm_int8_has_fp16_weight: bool = False
    
    def __post_init__(self):
        """Validate quantization configuration."""
        if self.load_in_4bit and self.load_in_8bit:
            raise ValueError("Cannot use both 4-bit and 8-bit quantization")
        
        if (self.load_in_4bit or self.load_in_8bit) and not HAS_BITSANDBYTES:
            raise ImportError(
                "bitsandbytes is required for quantization. "
                "Install with: pip install bitsandbytes"
            )
        
        if self.bnb_4bit_quant_type not in ["nf4", "fp4"]:
            raise ValueError(f"bnb_4bit_quant_type must be 'nf4' or 'fp4', got {self.bnb_4bit_quant_type}")


@dataclass
class BitsAndBytesConfig(QuantizationConfig):
    """Alias for QuantizationConfig for compatibility."""
    pass


@dataclass
class QLoRAConfig(LoRAConfig):
    """
    Configuration class for QLoRA (Quantized LoRA).
    
    Extends LoRAConfig with quantization parameters for memory-efficient training.
    """
    
    # Quantization parameters
    quantization_config: Optional[QuantizationConfig] = None
    
    def __post_init__(self):
        super().__post_init__()
        self.peft_type = "QLORA"
        
        # Create default quantization config if not provided
        if self.quantization_config is None:
            self.quantization_config = QuantizationConfig(load_in_4bit=True)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary."""
        config_dict = super().to_dict()
        if self.quantization_config:
            config_dict["quantization_config"] = self.quantization_config.__dict__
        return config_dict
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "QLoRAConfig":
        """Create config from dictionary."""
        quant_config = config_dict.pop("quantization_config", None)
        if quant_config:
            config_dict["quantization_config"] = QuantizationConfig(**quant_config)
        return cls(**{k: v for k, v in config_dict.items() if k != "peft_type"})


class QLoRALinear(LoRALinear):
    """QLoRA implementation for quantized Linear layers."""
    
    def __init__(self, base_layer: Union[nn.Linear, Linear4bit, Linear8bitLt], 
                 adapter_name: str = "default", r: int = 0, lora_alpha: int = 1,
                 lora_dropout: float = 0.0, fan_in_fan_out: bool = False,
                 init_lora_weights: bool = True, use_rslora: bool = False, **kwargs):
        
        # Store quantization info
        self.is_quantized = isinstance(base_layer, (Linear4bit, Linear8bitLt)) if HAS_BITSANDBYTES else False
        self.quantization_method = None
        
        if self.is_quantized and HAS_BITSANDBYTES:
            if isinstance(base_layer, Linear4bit):
                self.quantization_method = "4bit"
            elif isinstance(base_layer, Linear8bitLt):
                self.quantization_method = "8bit"
        
        super().__init__(base_layer, adapter_name, r, lora_alpha, lora_dropout,
                        fan_in_fan_out, init_lora_weights, use_rslora, **kwargs)
    
    def merge(self, safe_merge: bool = False, adapter_names: Optional[List[str]] = None):
        """Merge LoRA weights into quantized base layer."""
        if self.is_quantized:
            warnings.warn(
                "Merging LoRA weights into quantized layers is not recommended "
                "as it may degrade performance. Consider using unmerged inference."
            )
        
        # For quantized layers, we need special handling
        if self.is_quantized and HAS_BITSANDBYTES:
            # Dequantize, merge, and requantize (expensive operation)
            self._merge_quantized(safe_merge, adapter_names)
        else:
            # Standard merge for non-quantized layers
            super().merge(safe_merge, adapter_names)
    
    def _merge_quantized(self, safe_merge: bool = False, adapter_names: Optional[List[str]] = None):
        """Merge LoRA weights into quantized base layer."""
        if adapter_names is None:
            adapter_names = list(self.lora_A.keys())
        
        # This is a simplified implementation
        # In practice, you'd want to handle quantization more carefully
        for adapter_name in adapter_names:
            if adapter_name not in self.lora_A:
                continue
            
            if safe_merge and (torch.any(torch.isnan(self.lora_A[adapter_name].weight)) or
                             torch.any(torch.isnan(self.lora_B[adapter_name].weight))):
                warnings.warn(f"NaN detected in adapter {adapter_name}, skipping merge")
                continue
            
            # For quantized layers, we compute the LoRA delta and add it during forward pass
            # rather than actually merging into the quantized weights
            pass
        
        self.merged = True
    
    def forward(self, x: torch.Tensor, *args, **kwargs) -> torch.Tensor:
        """Forward pass through QLoRA linear layer."""
        if self.disable_adapters:
            return self.base_layer(x, *args, **kwargs)
        
        # Base layer forward (handles quantization automatically)
        result = self.base_layer(x, *args, **kwargs)
        
        if not self.merged:
            # Add LoRA contribution
            for adapter_name in self.lora_A.keys():
                lora_A = self.lora_A[adapter_name]
                lora_B = self.lora_B[adapter_name]
                dropout = self.lora_dropout[adapter_name]
                scaling = self.scaling[adapter_name]
                
                # LoRA forward: x @ A^T @ B^T * scaling
                # Use float32 for LoRA computation to maintain precision
                x_lora = x.to(lora_A.weight.dtype)
                lora_result = lora_B(lora_A(dropout(x_lora))) * scaling
                
                # Convert back to result dtype and add
                result = result + lora_result.to(result.dtype)
        
        return result


class QLoRAModel(LoRAModel):
    """
    QLoRA model implementation with quantization support.
    
    This class extends LoRAModel to support quantized base models for
    memory-efficient fine-tuning.
    """
    
    def __init__(self, model: nn.Module, config: QLoRAConfig, adapter_name: str = "default"):
        # Apply quantization to the model if needed
        if config.quantization_config and (config.quantization_config.load_in_4bit or config.quantization_config.load_in_8bit):
            model = self._quantize_model(model, config.quantization_config)
        
        super().__init__(model, config, adapter_name)
    
    def _quantize_model(self, model: nn.Module, quant_config: QuantizationConfig) -> nn.Module:
        """Apply quantization to the model."""
        if not HAS_BITSANDBYTES:
            raise ImportError("bitsandbytes is required for quantization")
        
        # This is a simplified quantization approach
        # In practice, you'd want to use transformers' quantization utilities
        def replace_linear_with_quantized(module):
            for name, child in module.named_children():
                if isinstance(child, nn.Linear):
                    if quant_config.load_in_4bit:
                        # Replace with 4-bit quantized linear
                        quantized_layer = Linear4bit(
                            child.in_features,
                            child.out_features,
                            bias=child.bias is not None,
                            compute_dtype=quant_config.bnb_4bit_compute_dtype,
                            compress_statistics=quant_config.bnb_4bit_use_double_quant,
                            quant_type=quant_config.bnb_4bit_quant_type,
                        )
                        # Copy weights
                        quantized_layer.weight.data = child.weight.data
                        if child.bias is not None:
                            quantized_layer.bias.data = child.bias.data
                        setattr(module, name, quantized_layer)
                    elif quant_config.load_in_8bit:
                        # Replace with 8-bit quantized linear
                        quantized_layer = Linear8bitLt(
                            child.in_features,
                            child.out_features,
                            bias=child.bias is not None,
                            has_fp16_weights=quant_config.llm_int8_has_fp16_weight,
                            threshold=quant_config.llm_int8_threshold,
                        )
                        # Copy weights
                        quantized_layer.weight.data = child.weight.data
                        if child.bias is not None:
                            quantized_layer.bias.data = child.bias.data
                        setattr(module, name, quantized_layer)
                else:
                    replace_linear_with_quantized(child)
        
        replace_linear_with_quantized(model)
        return model
    
    def _replace_module_with_lora(self, module_name: str, module: nn.Module, config: QLoRAConfig) -> None:
        """Replace a module with its QLoRA equivalent."""
        parent, target, target_name = self._get_submodules(self.base_model, module_name)
        
        if isinstance(target, (nn.Linear, Linear4bit, Linear8bitLt)) if HAS_BITSANDBYTES else isinstance(target, nn.Linear):
            new_module = QLoRALinear(
                target, self.adapter_name, config.r, config.lora_alpha,
                config.lora_dropout, config.fan_in_fan_out, config.init_lora_weights,
                config.use_rslora
            )
            # Replace the module
            setattr(parent, target_name, new_module)
            self.peft_modules[module_name] = new_module
        else:
            # Fall back to regular LoRA for unsupported layer types
            super()._replace_module_with_lora(module_name, module, config)
    
    def _get_submodules(self, model: nn.Module, key: str) -> Tuple[nn.Module, nn.Module, str]:
        """Get parent module, target module, and target name from a key."""
        parent = model
        target_name = key.split(".")[-1]
        
        for attr in key.split(".")[:-1]:
            parent = getattr(parent, attr)
        
        target = getattr(parent, target_name)
        return parent, target, target_name
    
    def get_memory_footprint(self) -> Dict[str, float]:
        """Get memory footprint information for the quantized model."""
        total_params = 0
        quantized_params = 0
        lora_params = 0
        
        for name, param in self.base_model.named_parameters():
            param_size = param.numel()
            total_params += param_size
            
            if "lora_" in name:
                lora_params += param_size
            elif hasattr(param, 'quant_state'):
                # This is a quantized parameter
                quantized_params += param_size
        
        # Estimate memory usage (rough approximation)
        base_memory = (total_params - lora_params - quantized_params) * 4  # FP32 bytes
        quantized_memory = quantized_params * 1  # Assume 1 byte per quantized param
        lora_memory = lora_params * 4  # LoRA params are typically FP32
        
        total_memory = base_memory + quantized_memory + lora_memory
        
        return {
            "total_params": total_params,
            "quantized_params": quantized_params,
            "lora_params": lora_params,
            "base_memory_mb": base_memory / (1024 * 1024),
            "quantized_memory_mb": quantized_memory / (1024 * 1024),
            "lora_memory_mb": lora_memory / (1024 * 1024),
            "total_memory_mb": total_memory / (1024 * 1024),
            "memory_reduction": 1 - (total_memory / (total_params * 4))
        }
