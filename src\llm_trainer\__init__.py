"""
LLM Trainer: A complete framework for training Large Language Models from scratch.

This package provides:
- Custom Transformer architecture implementation
- BPE tokenizer from scratch
- wordpiece tokenizer
- Data loading and preprocessing pipelines
- Training infrastructure with distributed support
- Inference and generation capabilities
"""

__version__ = "0.2.1"
__author__ = "OEvortex"

from .models import TransformerLM
from .tokenizer import BPETokenizer
from .training import Trainer
from .config import ModelConfig, TrainingConfig

# Import PEFT functionality
from . import peft
from .peft import (
    # Base PEFT classes
    PEFTConfig,
    PEFTModel,
    get_peft_model,
    TaskType,

    # LoRA
    LoRAConfig,
    LoRAModel,

    # QLoRA
    QLoRAConfig,
    QLoRAModel,
    QuantizationConfig,
    BitsAndBytesConfig,

    # Utilities
    mark_only_lora_as_trainable,
    get_peft_model_state_dict,
    set_peft_model_state_dict,
    merge_and_unload,
    save_peft_model,
    load_peft_model,
    print_trainable_parameters
)

__all__ = [
    # Core components
    "TransformerLM",
    "BPETokenizer",
    "Trainer",
    "ModelConfig",
    "TrainingConfig",

    # PEFT module
    "peft",

    # Base PEFT classes
    "PEFTConfig",
    "PEFTModel",
    "get_peft_model",
    "TaskType",

    # LoRA
    "LoRAConfig",
    "LoRAModel",

    # QLoRA
    "QLoRAConfig",
    "QLoRAModel",
    "QuantizationConfig",
    "BitsAndBytesConfig",

    # Utilities
    "mark_only_lora_as_trainable",
    "get_peft_model_state_dict",
    "set_peft_model_state_dict",
    "merge_and_unload",
    "save_peft_model",
    "load_peft_model",
    "print_trainable_parameters"
]
